/* eslint-disable */
"use client"

import {
  FacebookIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
  TelegramShareButton,
  TelegramIcon,
  LinkedinShareButton,
  LinkedinIcon,
  RedditShareButton,
  RedditIcon,
  EmailShareButton,
  EmailIcon,
} from "next-share"
import { Copy } from "lucide-react"
import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { getShortDealUrl, isShortDealFormat, getLongDealUrl } from "@/lib/utils"
import { extractDealIdFromSlug } from "@/lib/slug"

interface ShareButtonsProps {
  /** Page URL to share. If omitted it falls back to window.location.href (client-side). */
  url?: string
  /** Title / caption for the share */
  title: string
  /** Show admin copy button */
  showAdminCopy?: boolean
}

export default function ShareButtons({ url, title, showAdminCopy = true }: ShareButtonsProps) {
  const { data: session } = useSession()
  const [shareUrl, setShareUrl] = useState(url || "")
  const [facebookUrl, setFacebookUrl] = useState(url || "")
  const [copySuccess, setCopySuccess] = useState(false)

  // Set up URLs for sharing - short for most platforms, long SEO-friendly for Facebook
  useEffect(() => {
    const setupUrls = (baseUrl: string, pathname: string) => {
      const dealMatch = pathname.match(/^\/deals\/(.+)$/);
      if (dealMatch) {
        const dealParam = dealMatch[1];
        let dealId: number | null = null;
        let dealTitle = "";

        if (isShortDealFormat(dealParam)) {
          // Short format - we have the ID but need title for Facebook long URL
          dealId = parseInt(dealParam, 10);
          setShareUrl(getShortDealUrl(dealId));
          // For Facebook, we need the title to generate the long URL
          // Extract just the deal title from the share text (remove "Nicedeals UK presents" prefix)
          const cleanTitle = title.replace(/^Nicedeals UK presents\s+/, '').split(' on ')[0];
          setFacebookUrl(getLongDealUrl(cleanTitle, dealId));
          return;
        } else {
          // Long format - extract ID and title from slug
          dealId = extractDealIdFromSlug(dealParam);
          if (dealId) {
            // Extract title from slug (remove ID at end)
            const lastHyphenIndex = dealParam.lastIndexOf('-');
            dealTitle = lastHyphenIndex > 0 ? dealParam.substring(0, lastHyphenIndex) : dealParam;

            setShareUrl(getShortDealUrl(dealId));
            setFacebookUrl(getLongDealUrl(dealTitle, dealId));
            return;
          }
        }
      }

      // Fallback - use the same URL for both
      setShareUrl(baseUrl);
      setFacebookUrl(baseUrl);
    };

    if (!url && typeof window !== "undefined") {
      const currentUrl = window.location.href;
      const pathname = window.location.pathname;
      setupUrls(currentUrl, pathname);
    } else if (url) {
      try {
        const urlObj = new URL(url);
        setupUrls(url, urlObj.pathname);
      } catch {
        // Invalid URL, use as-is
        setShareUrl(url);
        setFacebookUrl(url);
      }
    }
  }, [url, title])

  // Copy to clipboard function
  const handleCopyPromoText = async () => {
    try {
      await navigator.clipboard.writeText(title)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = title
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    }
  }

  const iconSize = 32
  const isAdmin = session?.user?.id === "1"

  return (
    <div className="space-y-4">
      {/* Share buttons grid - square with small rounded corners */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => {
            const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(facebookUrl)}`;
            window.open(shareUrl, 'facebook-share', 'width=580,height=296');
          }}
          className="w-10 h-10 flex items-center justify-center rounded-md transition-colors overflow-hidden"
          title="Share on Facebook"
        >
          <FacebookIcon size={40} round={false} />
        </button>

        <TwitterShareButton url={shareUrl} title={title}>
          <div className="w-10 h-10 flex items-center justify-center rounded-md transition-colors overflow-hidden">
            <TwitterIcon size={40} round={false} />
          </div>
        </TwitterShareButton>

        <WhatsappShareButton url={shareUrl} title={title} separator=" – ">
          <div className="w-10 h-10 flex items-center justify-center rounded-md transition-colors overflow-hidden">
            <WhatsappIcon size={40} round={false} />
          </div>
        </WhatsappShareButton>

        <TelegramShareButton url={shareUrl} title={title}>
          <div className="w-10 h-10 flex items-center justify-center rounded-md transition-colors overflow-hidden">
            <TelegramIcon size={40} round={false} />
          </div>
        </TelegramShareButton>

        <LinkedinShareButton url={shareUrl} title={title} summary={title} source="NiceDeals">
          <div className="w-10 h-10 flex items-center justify-center rounded-md transition-colors overflow-hidden">
            <LinkedinIcon size={40} round={false} />
          </div>
        </LinkedinShareButton>

        <RedditShareButton url={shareUrl} title={title}>
          <div className="w-10 h-10 flex items-center justify-center rounded-md transition-colors overflow-hidden">
            <RedditIcon size={40} round={false} />
          </div>
        </RedditShareButton>

        <EmailShareButton url={shareUrl} subject={title} body={title + " – " + shareUrl}>
          <div className="w-10 h-10 flex items-center justify-center rounded-md transition-colors overflow-hidden">
            <EmailIcon size={40} round={false} />
          </div>
        </EmailShareButton>
      </div>

      {/* Admin Copy Button */}
      {showAdminCopy && isAdmin && (
        <button
          onClick={handleCopyPromoText}
          className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            copySuccess 
              ? 'bg-green-100 text-green-700 border border-green-200' 
              : 'bg-blue-100 text-blue-700 border border-blue-200 hover:bg-blue-200'
          }`}
        >
          <Copy className="w-4 h-4" />
          <span>{copySuccess ? 'Copied!' : 'Copy Promo Text'}</span>
        </button>
      )}
    </div>
  )
}
